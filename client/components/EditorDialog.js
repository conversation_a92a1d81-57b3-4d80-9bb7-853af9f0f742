"use client";

import React, { useRef, useCallback, useMemo } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import SaveIcon from '@mui/icons-material/Save';

import { apiUrl } from "@/lib/fetcher";

import CKEditor from './CKEditor';

const EditorDialog = React.memo(({open, onClose, textValue = '', elName = 'ckeditor', onDone}) => {
  const editorRef = useRef(null);

  const handleEditorReady = useCallback((editor) => {
    editorRef.current = editor;
    editor.on('dataReady', () => {
      editor._isContentReady = true;
      console.log('[CKEditor] dataReady: content is safe to get');
    });
  }, []);

  const handleSave = useCallback(() => {
    const editor = editorRef.current;
    if (editor && editor._isContentReady) {
      setTimeout(() => {
        try {
          const content = editorRef.current.getData();
          console.log("✅ Nội dung đã xử lý: ", content);
          onDone(content);
          onClose();
        } catch (err) {
          console.error("❌ Lỗi khi gọi getData:", err);
        }
      }, 0); // delay để WIRIS hoàn thành DOM render
    } else {
      console.warn("⏳ Chưa sẵn sàng để lấy dữ liệu. Vui lòng thử lại sau.");
    }
  }, [onDone, onClose]);

  // const handleEditorChange = useCallback((event) => {
  //   const content = event.editor.getData();
  // }, []);

  return (
    <Dialog
      fullWidth={true}
      maxWidth={'lg'}
      open={open}
      onClose={onClose}
      disableEnforceFocus
      disableAutoFocus
    >
      <DialogTitle className="h5">
        Nhập nội dung:
      </DialogTitle>
      <DialogContent>
        <CKEditor
          config={{
              height: 300,
              toolbar: "EditorQuiz",
              // imgurClientId: "ae5cff9f2dddc66",
              allowedContent: true,
              extraAllowedContent: '*[*]{*}(*); math[*]; mi[*]; mo[*]; mn[*]; ms[*]; mtext[*]; mrow[*]; mfrac[*]; msqrt[*]; mroot[*]; mstyle[*]; merror[*]; mpadded[*]; mphantom[*]; mfenced[*]; menclose[*]; msub[*]; msup[*]; msubsup[*]; munder[*]; mover[*]; munderover[*]; mmultiscripts[*]; mtable[*]; mtr[*]; mtd[*]; maligngroup[*]; malignmark[*]; mlabeledtr[*]; maction[*]; semantics[*]; annotation[*]; annotation-xml[*]',
              folderImg: "qimgs",
              folderMp3: "qaudios",
              imgUpload: apiUrl('/image/upload'),
              mediaUpload: apiUrl('/media/upload'),
              readOnly: false,
              // Math configuration
              extraPlugins: 'mathjax,ckeditor_wiris',
              mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/3.2.2/es5/tex-mml-chtml.js',
              mathJaxClass: 'math-tex',
              // Enable formula editing on double-click
              on: {
                instanceReady: function(evt) {
                  const editor = evt.editor;
                  // Make math formulas editable
                  editor.on('doubleclick', function(evt) {
                    const element = evt.data.element;
                    if (element && (element.hasClass('math-tex') || element.is('math') || element.hasClass('Wirisformula'))) {
                      evt.data.dialog = 'mathjax';
                      if (editor.commands.ckeditor_wiris_formulaEditor) {
                        editor.commands.ckeditor_wiris_formulaEditor.exec();
                      } else if (editor.commands.mathjax) {
                        editor.commands.mathjax.exec();
                      }
                    }
                  });
                }
              }
          }}
          initData={textValue}
          name={elName}
          type="classic"
          onInstanceReady={handleEditorReady}
          // onChange={handleEditorChange}
        />
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'space-between' }}>
        <Button variant="contained" onClick={onClose} color="error" size="small">
          Hủy
        </Button>
        <Button
          size="small"
          onClick={handleSave}
          variant="contained"
          color="secondary"
          startIcon={<SaveIcon fontSize="small" />}
        >
          Xong
        </Button>
      </DialogActions>
    </Dialog>
  );
});

EditorDialog.displayName = "EditorDialog";

export default EditorDialog;
