export const siteMetadata = {
  title: process.env.SITE_TITLE || '<PERSON>hi online cùng VietJack với kho đề thi đầy đủ các môn',
  description: process.env.SITE_DESCRIPTION || '<PERSON><PERSON><PERSON><PERSON> thi trắc nghi<PERSON> online, đề thi họ<PERSON> k<PERSON>, đề thi thử các m<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> các lớp tiểu học, THCS, THPT',
  siteName: process.env.SITE_NAME || 'vietjack.com',
  siteUrl: new URL(process.env.NEXT_PUBLIC_HOST_URL || 'http://localhost:3000'),
  author: process.env.SITE_AUTHOR || 'VietJack',
};

export const userCookieName = process.env.USER_INFO_COOKIE_NAME || '_u_info_';
export const keyStorageUser = 'vj-user';
export const keyStorageIsLoginGoogle = 'is_login_google';
export const keyCustomization = 'vjtheme-config';

export const defaultCustomization = {
  isOpen: [], // for active default menu
  defaultId: 'default',
  fontFamily: `'Roboto', sans-serif`,
  borderRadius: 8,
  borderCard: '1px solid #09090933',
  opened: true,
  mode: 'light', // dark
  presetColor: 'red',
  outlinedFilled: true,
};

export const gridSpacing = 3;
export const drawerWidth = 260;
export const appDrawerWidth = 320;
export const perPage = 7;
export const mcqPerOption = 5;

export const orderOptions = [
  { value: "latest", label: "Mới nhất" },
  { value: "oldest", label: "Cũ nhất" },
  { value: "alphabetical", label: "Bảng chữ cái" },
];

export const colorPickerOptions = [
  { value: "#f44336", label: "#f44336" },
  { value: "#3f51b5", label: "#3f51b5" },
  { value: "#4cae50", label: "#4cae50" },
  { value: "#ffc108", label: "#ffc108" },
  { value: "#9e9e9e", label: "#9e9e9e" },
  { value: "#1e272e", label: "#1e272e" },
];

export const optionBackgrounds = [
  "#2f6dae",
  "#2c9ca6",
  "#eca82c",
  "#d4546a",
  "#9a4292",
];

export const questionTypes = {
  QUIZX: {
    icon: '<i class="bi bi-check-circle-fill"></i>',
    initQue: {
      content: "",
      options: [
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
        {
          content: "",
          isCorrect: false,
        },
      ],
      answer: [],
    },
  },
  BLANK: {
    icon: '<i class="bi bi-dash-square-fill"></i>',
    initQue: {
      content: "",
    },
  },
};

export const roleMessages = {
  admin: 'Chào mừng Quản trị viên! Bạn có quyền truy cập đầy đủ vào hệ thống.',
  editor: 'Chào mừng Biên tập viên! Hãy bắt đầu tạo nội dung chất lượng.',
  teacher: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu tạo nội dung giáo dục.',
  student: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu học tập ngay.',
  trainer: 'Chào mừng Trainer! Hãy bắt đầu tạo các khóa đào tạo chuyên nghiệp.'
};
